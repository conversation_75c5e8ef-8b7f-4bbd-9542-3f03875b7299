import { useMemo } from "react";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import { useAuthStore } from "@/stores/auth-store";
import { usePathname } from "next/navigation";

const useAgentAnalytics = () => {
  const analytics = useRudderStackAnalytics();
  const { user } = useAuthStore((state) => state);
  const pathname = usePathname();

  return useMemo(() => {
    const track = (
      eventName: string,
      payload: Record<string, unknown> = {},
    ) => {
      if (!analytics) return;
      analytics.track(eventName, {
        user_id: user?.id,
        email: user?.email,
        domain: user?.email.split("@")[1],
        category: "Agents",
        timestamp: new Date().toISOString(),
        app: "test.ai",
        page_name: pathname,
        ...payload,
      });
    };

    return {
      trackModalOpened: (source_location: string) =>
        track("agent_creation_modal_opened", { source_location }),
      trackNameEntered: (name: string) =>
        track("agent_name_entered", {
          agent_name: name,
          character_count: name.length,
        }),
      trackContactNumberEntered: (number: string) => {
        const country_code = number.startsWith("+") ? number.slice(1, 4) : "";
        const number_length = number.length;
        track("agent_contact_number_entered", { country_code, number_length });
      },
      trackLanguageSelected: (language: string, default_selected: boolean) =>
        track("agent_language_selected", { language, default_selected }),
      trackDescriptionEntered: (description: string) => {
        const keywords = ["hospital", "real estate", "restaurant"];
        const contains_keywords = keywords.some((keyword) =>
          description.toLowerCase().includes(keyword),
        );
        track("agent_description_entered", {
          character_count: description.length,
          contains_keywords,
        });
      },
      trackFieldValidationError: (field_name: string, error_message: string) =>
        track("agent_field_validation_error", {
          field_name,
          error_type: "validation",
          error_message,
        }),
      trackCreationSubmitted: (
        form_completion_time: number,
        all_fields_filled: boolean,
      ) =>
        track("agent_creation_submitted", {
          form_completion_time,
          all_fields_filled,
        }),
      trackCreationCompleted: (
        agent_id: string,
        agent_name: string,
        time_to_complete: number,
      ) =>
        track("agent_creation_completed", {
          agent_id,
          agent_name,
          time_to_complete,
        }),
      trackCreationAbandoned: (
        time_spent: number,
        fields_filled: number,
        modal_close_method: string,
      ) =>
        track("agent_creation_abandoned", {
          time_spent,
          fields_filled,
          modal_close_method,
        }),
    };
  }, [analytics, user?.id]); // Добавляем зависимости
};

export default useAgentAnalytics;
