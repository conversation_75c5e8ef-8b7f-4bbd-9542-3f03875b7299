"use client";
import React, { useEffect, useState, FocusEvent } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  FormControl,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
  InputAdornment,
  Menu,
  CircularProgress,
} from "@mui/material";

import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
import PhoneIcon from "@mui/icons-material/Phone";
import CloseIcon from "@mui/icons-material/Close";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';

import { useGeneralStore } from "@/providers/general-store-provider";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import useAgentAnalytics from "@/app/ui/AgentScreen/components/UpdateAgentModal/useAgentAnalytics";
import { useRouter, usePathname } from "next/navigation";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import { IAgent } from "@/types/agent";
import Image from "next/image";
import { useAgentDescription } from '@/hooks/useAgentDescription';
import { useOnboardingStore } from '@/stores/onboarding-store';
import useDataSet from '@/hooks/useDataSet';
import { UploadedFile } from '@/hooks/useFileUpload';
import useAgentTemplates from '@/hooks/useAgentTemplates';
import { AgentTemplate } from '@/hooks/useAgentTemplates';
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Routes from "@/constants/routes";
import { createAgent, updateAgent } from "../../lib/fetchers";
import FileUpload from "@/components/FileUpload/FileUpload";
      



export interface IUpdateAgentForm {
  name: string;
  contactNumber?: string;
  language: string;
  description: string;
  knowledge_base_ids?: number[];
  knowledge_base_files?: UploadedFile[];
}

interface IGenerateScenariosModalProps {
  onCancel: () => void;
  isModalOpen: boolean;
  agentId: string | null;
  onDataChange?: (data: IUpdateAgentForm) => void;
  initialData?: IUpdateAgentForm;
  onSuccess?: () => void;
}

const UpdateAgentModal = ({
  onCancel,
  isModalOpen,
  agentId,
  onDataChange,
  initialData,
  onSuccess,
}: IGenerateScenariosModalProps) => {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const analyticsEvents = useAgentAnalytics();
  const notify = useNotification();
  const { agents, setAgents, setCurrentAgentId } = useGeneralStore(
    (state) => state
  );
  const router = useRouter();
  const pathname = usePathname();
  const isStarterRoute = pathname?.includes('starter');
  const { isGenerating, generateDescription } = useAgentDescription();
  const { setStepComplete, nextStep } = useOnboardingStore();
  const [selectedFiles, setSelectedFiles] = useState<UploadedFile[]>([]);
  const { dataSet, fetchDataSet, deleteFile } = useDataSet();
  const { templates, isLoading: isTemplatesLoading } = useAgentTemplates();
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);
  const queryClient = useQueryClient();

  const currentAgent = agents.find((agent: IAgent) => agent.id === agentId);

  // Local state for the form fields
  const [name, setName] = useState<string>(initialData?.name || "");
  const [contactNumber, setContactNumber] = useState<string>(
    initialData?.contactNumber || ""
  );
  const [language, setLanguage] = useState<string>(
    initialData?.language || "english"
  );
  const [description, setDescription] = useState<string>(
    initialData?.description || ""
  );

  const [loading, setLoading] = useState<boolean>(false);
  const [submittable, setSubmittable] = useState<boolean>(false);
  const [modalOpenedTime, setModalOpenedTime] = useState<number | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const AGENT_NAME_MAX_LENGTH = 50;

  // Agent creation mutation
  const createAgentMutation = useMutation({
    mutationFn: createAgent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      notify.success({ message: 'Agent created successfully', description: 'The agent has been added to your list' });
      generalAnalyticsEvents.trackApiRequestFailed('Create Agent', '200', 'Agent created successfully');
    },
    onError: (error: any) => {
      console.log('error', error);
      notify.error({ message: 'Failed to create agent', description: error?.response.data.detail || 'An unexpected error occurred' });
      generalAnalyticsEvents.trackApiRequestFailed('Create Agent', error?.response?.status ? String(error.response.status) : '500', error?.message || 'Failed to create agent');
    }
  });


  // Agent editing mutation
  const updateAgentMutation = useMutation({
    mutationFn: updateAgent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      notify.success({ message: 'Agent updated successfully', description: 'The agent has been updated' });
      generalAnalyticsEvents.trackApiRequestFailed('Edit Agent', '200', 'Agent updated successfully');
    },
    onError: (error: any) => {
      notify.error({ message: 'Failed to update agent', description: error?.response.data.detail || 'An unexpected error occurred' });
      generalAnalyticsEvents.trackApiRequestFailed('Edit Agent', error?.response?.status ? String(error.response.status) : '500', error?.message || 'Failed to update agent');
    }
  });

  useEffect(() => {
    if (isModalOpen) {
      setModalOpenedTime(Date.now());
      analyticsEvents.trackModalOpened("UpdateAgentModal");
    }
  }, [isModalOpen, analyticsEvents]);

  useEffect(() => {
    if (agentId && currentAgent) {
      setName(currentAgent.name || "");
      setContactNumber(currentAgent.contact_number || "");
      setLanguage(currentAgent.language || "english");
      setDescription(currentAgent.description || "");
    }
  }, [agentId, currentAgent]);

  // If creating, sync with initialData
  useEffect(() => {
    if (!agentId && initialData) {
      setName(initialData.name);
      setContactNumber(initialData.contactNumber || "");
      setLanguage("english");
      setDescription(initialData.description || "");
    }
  }, [agentId, initialData]);

  // Load existing files when editing an agent
  useEffect(() => {
    if (agentId && currentAgent?.knowledge_base_ids) {
      // Find the files in the data set that match the knowledge_base_ids
      const existingFiles = dataSet.filter(file =>
        currentAgent.knowledge_base_ids?.includes(file.id)
      );
      setSelectedFiles(existingFiles);
    }
  }, [agentId, currentAgent, dataSet]);

  // Emit data changes upward & simple validation
  useEffect(() => {
    if (onDataChange) {
      onDataChange({
        name,
        contactNumber,
        language,
        description,
        knowledge_base_ids: selectedFiles.map(file => file.id),
        knowledge_base_files: selectedFiles
      });
    }
    if (
      name.trim() &&
      name.length <= AGENT_NAME_MAX_LENGTH &&
      contactNumber.trim() &&
      language.trim() &&
      description.trim()
    ) {
      setSubmittable(true);
    } else {
      setSubmittable(false);
    }
  }, [name, contactNumber, language, description, selectedFiles, onDataChange]);

  // Handlers for field interactions
  const handleNameBlur = (e: FocusEvent<HTMLInputElement>) => {
    analyticsEvents.trackNameEntered(e.target.value);
  };

  const handleContactNumberBlur = (
    e: FocusEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    analyticsEvents.trackContactNumberEntered(e.target.value);
  };

  const handleLanguageSelect = (value: string) => {
    let default_selected = false;
    if (agentId && currentAgent && value === currentAgent.language) {
      default_selected = true;
    }
    setLanguage(value);
    analyticsEvents.trackLanguageSelected(value, default_selected);
  };

  const handleDescriptionBlur = (e: FocusEvent<HTMLTextAreaElement>) => {
    analyticsEvents.trackDescriptionEntered(e.target.value);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleTemplateSelect = (template: AgentTemplate) => {
    setDescription(template.description);
    setSelectedTemplateId(template.id);
    handleMenuClose();
  };

  const handleFileUploaded = (file: UploadedFile) => {
    setSelectedFiles(prev => [...prev, file]);
    fetchDataSet(); // Refresh the data set
  };

  const handleFileRemoved = async (fileId: string | number) => {
    if (agentId) {
      await deleteFile(Number(fileId), agentId);
    }
    setSelectedFiles(prev => prev.filter(file => file.id !== Number(fileId)));
  };

  // Submit logic
  const handleUpdateAgent = async () => {
    if (modalOpenedTime) {
      const now = Date.now();
      const form_completion_time = now - modalOpenedTime;
      analyticsEvents.trackCreationSubmitted(form_completion_time, true);
    }
    setLoading(true);
    const formData = {
      name,
      contact_number: contactNumber,
      language,
      description,
      knowledge_base_ids: selectedFiles.map(file => file.id),
      knowledge_base_files: selectedFiles
    };
    if (agentId) {
      updateAgentMutation.mutate(
        { agentId, agentData: formData },
        {
          onSuccess: () => {
            if (onSuccess) onSuccess();
            onCancel();
            setLoading(false);
          },
          onError: () => {
            setLoading(false);
          }
        }
      );
      return;
    } else {
      createAgentMutation.mutate(
        formData,
        {
          onSuccess: (data) => {
            if (onSuccess) onSuccess();
            onCancel();
            setLoading(false);
            setAgents(agents.concat(data));
            const getLastAgent = agents.concat(data)[agents.concat(data).length - 1];
            setCurrentAgentId(getLastAgent.id);
            setStepComplete('createAgent');
            nextStep();
            if (isStarterRoute) {
              router.push(Routes.scenario);
            }
          },
          onError: () => {
            setLoading(false);
          }
        }
      );
      return;
    }
  };

  const handleCancel = () => {
    const now = Date.now();
    const time_spent = modalOpenedTime ? now - modalOpenedTime : 0;
    const fields_filled = [name, contactNumber, language, description].filter(Boolean).length;

    analyticsEvents.trackCreationAbandoned(time_spent, fields_filled, "cancel");
    generalAnalyticsEvents.trackModalCloseButtonClicked(
      "UpdateAgentModal",
      time_spent,
      `${fields_filled}/4`
    );
    onCancel();
  };

  const handleGenerateDescription = async () => {
    try {
      if (!description) {
        notify.error({
          message: "Description is empty",
          description: "please fill in the description manually or select a template",
        });
        return;
      }

      const response = await generateDescription(description, selectedTemplateId);
      setDescription(response.description);
    } catch (error) {
      console.warn(error);
    }
  };


  return (
    <Dialog
      open={isModalOpen}
      onClose={handleCancel}
      maxWidth="sm"
      fullWidth
      slotProps={{
        paper: {
          sx: { borderRadius: '24px', width: 600, backgroundColor: '#FFFFFF' },
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          p: 2,
          borderBottom: "none",
          "&.MuiDialogTitle-root": { borderBottom: "none" },
        }}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <Box
            sx={{
              width: 48,
              height: 48,
              borderRadius: '16px',
              background: '#fff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 2px 8px 0 rgba(16, 24, 40, 0.10)',
            }}
          >
            <Image src="ai-scan.svg" alt="Metric" width={28} height={28} />
          </Box>
          <Typography
            variant="h5"
            sx={{ fontWeight: 700, fontFamily: "Plus Jakarta Sans" }}
          >
            {agentId ? "Update Agent" : "Create new agent"}
          </Typography>
        </Box>
        <Box>
          <IconButton
            aria-label="close"
            onClick={handleCancel}
            size="small"
            sx={{ color: "text.secondary" }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box component="form" sx={{ mt: 1 }}>
          {/* Agent Name */}
          <FormControl fullWidth sx={{ mb: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
              >
                Agent Name*
              </Typography>
            </Box>
            <TextField
              id="agent-name"
              required
              value={name}
              placeholder=" e.g., Superchat Assistant, Booking Bot"
              autoComplete="off"
              onChange={(e) => setName(e.target.value)}
              onBlur={handleNameBlur}
              inputProps={{ maxLength: AGENT_NAME_MAX_LENGTH }}
              error={name.length > AGENT_NAME_MAX_LENGTH}
              helperText={`${name.length}/${AGENT_NAME_MAX_LENGTH} characters`}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ color: "#667085" }}>
                    <PersonOutlineIcon />
                  </InputAdornment>
                ),
              }}
              sx={{
                fontFamily: "Plus Jakarta Sans",
                "& .MuiOutlinedInput-root": {
                  borderRadius: "12px",
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#7F56D9",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#7F56D9",
                  },
                },
              }}
            />
          </FormControl>

          {/* Contact Number */}
          <FormControl fullWidth sx={{ mb: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
              >
                Contact Number*
              </Typography>
              <Tooltip
                sx={{ fontFamily: "Plus Jakarta Sans" }}
                title=" Include country code (e.g., +1 for USA)"
                placement="right"
                onOpen={() =>
                  generalAnalyticsEvents.trackHelpIconClicked(
                    "UpdateAgentModal",
                    "Contact Number"
                  )
                }
              >
                <InfoOutlinedIcon fontSize="small" sx={{ ml: 1 }} />
              </Tooltip>
            </Box>
            <TextField
              id="contact-number"
              placeholder="+e.g., +971501234567"
              required
              autoComplete="off"
              value={contactNumber}
              onChange={(e) => setContactNumber(e.target.value)}
              onBlur={handleContactNumberBlur}
              inputProps={{ pattern: "^\\+[0-9]{1,15}$" }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ color: "#667085" }}>
                    <PhoneIcon />
                  </InputAdornment>
                ),
              }}
              sx={{
                fontFamily: "Plus Jakarta Sans",
                "& .MuiOutlinedInput-root": {
                  borderRadius: "12px",
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#7F56D9",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#7F56D9",
                  },
                },
              }}
            />
            {/* <Typography
              variant="caption"
              color="text.secondary"
              sx={{ mt: 0.5, fontFamily: "Plus Jakarta Sans" }}
            >
              Please insert the number to call, including country code (e.g., +1 for USA)
            </Typography> */}
          </FormControl>

          {/* Language */}
          <FormControl fullWidth sx={{ mb: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
              >
                Language*
              </Typography>
            </Box>
            <Select
              id="language"
              value={language}
              onChange={(e) => handleLanguageSelect(e.target.value)}
              displayEmpty
              autoComplete="off"
              required
              sx={{
                fontFamily: "Plus Jakarta Sans",
                borderRadius: "12px",
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#7F56D9",
                },
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#7F56D9",
                },
              }}
            >
              <MenuItem value="" disabled>
                Select
              </MenuItem>
              <MenuItem value="english">English</MenuItem>
              <MenuItem value="arabic">Arabic</MenuItem>
            </Select>
          </FormControl>

          {/* Description with embedded buttons */}
          <FormControl fullWidth sx={{ mb: 2, position: "relative" }}>
            <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 1 }}>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Typography
                  variant="subtitle1"
                  sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
                >
                  Description*
                </Typography>
                <Tooltip
                  sx={{ fontFamily: "Plus Jakarta Sans" }}
                  title="This description will be used to perform the scenario"
                  placement="right"
                  onOpen={() =>
                    generalAnalyticsEvents.trackHelpIconClicked(
                      "UpdateAgentModal",
                      "Description"
                    )
                  }
                >
                  <InfoOutlinedIcon fontSize="small" sx={{ ml: 1 }} />
                </Tooltip>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  endIcon={<ArrowDropDownIcon />}
                  onClick={handleMenuClick}
                  sx={{
                    fontFamily: "Plus Jakarta Sans",
                    textTransform: "none",
                    borderRadius: '16px',
                    border: '1px solid #D0D5DD',
                    minWidth: '157px',
                    height: '36px',
                    fontSize: "14px",
                    py: 2,
                    color: '#101828',
                    boxShadow: 'none',
                    backgroundColor: '#fffff',
                  }}
                >
                  Templates
                </Button>
                <Button
                  startIcon={<Image src="sparkles.svg" alt="Auto Generate" width={20} height={20} />}
                  onClick={handleGenerateDescription}
                  disabled={isGenerating}
                  sx={{
                    fontFamily: "Plus Jakarta Sans",
                    textTransform: "none",
                    borderRadius: '16px',
                    border: '1px solid #D0D5DD',
                    minWidth: '157px',
                    height: '36px',
                    fontSize: "14px",
                    py: 2,
                    color: '#101828',
                    boxShadow: 'none',
                    backgroundColor: '#fffff',
                  }}
                >
                  {isGenerating ? "Generating..." : "Auto Generate"}
                </Button>
              </Box>
            </Box>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              PaperProps={{
                sx: {
                  mt: 1,
                  minWidth: 200,
                  borderRadius: '12px',
                  boxShadow: '0px 4px 6px -1px rgba(0, 0, 0, 0.1), 0px 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }
              }}
            >
              {isTemplatesLoading ? (
                <MenuItem disabled>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  Loading templates...
                </MenuItem>
              ) : (
                templates.map((template) => (
                  <MenuItem
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    sx={{
                      fontFamily: "Plus Jakarta Sans",
                      fontSize: "14px",
                      '&:hover': {
                        backgroundColor: "#F9F5FF"
                      }
                    }}
                  >
                    {template.name}
                  </MenuItem>
                ))
              )}
            </Menu>
            <Box sx={{ position: "relative" }}>
              <TextField
                id="description"
                required
                multiline
                autoComplete="off"
                placeholder="Describe your agent's purpose, e.g., Helps users book hotels or order food via voice."
                rows={4}
                fullWidth
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                onBlur={handleDescriptionBlur}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    fontFamily: "Plus Jakarta Sans",
                    borderRadius: "12px",
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#7F56D9",
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#7F56D9",
                    },
                  },
                }}
              />
            </Box>
          </FormControl>

          {/* Add File Upload Component */}
          <FormControl fullWidth sx={{ mb: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
              >
                Knowledge Base Files
              </Typography>
              <Tooltip
                sx={{ fontFamily: "Plus Jakarta Sans" }}
                title="Upload PDF files to enhance your agent's knowledge base"
                placement="right"
              >
                <InfoOutlinedIcon fontSize="small" sx={{ ml: 1 }} />
              </Tooltip>
            </Box>
            <FileUpload
              onFileUploaded={handleFileUploaded}
              onFileRemoved={handleFileRemoved}
              selectedFiles={selectedFiles}
              agentId={agentId || undefined}
            />
          </FormControl>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, borderTop: "none" }}>
        <Button
          fullWidth
          size="large"
          onClick={handleUpdateAgent}
          variant="contained"
          color="primary"
          sx={{
            backgroundColor: "#7F56D9",
            fontFamily: "Plus Jakarta Sans",
            textTransform: "capitalize",
            borderRadius: "12px",
            height: "50px",
          }}
          disabled={!submittable || loading}
        >
          {loading ? "Processing..." : agentId ? "Update Agent" : "Create Agent"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UpdateAgentModal;
