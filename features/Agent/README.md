# Agents Dashboard Module

## Structure
- `page.tsx`: Main page, handles state and composition.
- `components/`: All presentational and modal components.
- `styles.module.scss`: Scoped styles for the module.

## Best Practices
- All business logic in hooks (see `useAgentLogic`).
- All presentational components are pure and memoized with `React.memo`.
- Error and success handled via MUI Snackbar.
- Strict TypeScript types for all props and state.
- Accessibility: All interactive elements should be accessible (aria, keyboard, focus management).

## Testing
- Use React Testing Library for all components.
- Place tests in a `__tests__/` folder or alongside components as `*.test.tsx`.

## Conventions
- Use MUI theme variables for colors, spacing, and typography.
- Remove unused code and keep naming consistent.
- Document all exported functions/components with JSDoc.

## Extending
- For new features, add new components to the `components/` folder.
- For new hooks, add a `hooks/` folder.
- For utilities, add a `utils/` folder. 