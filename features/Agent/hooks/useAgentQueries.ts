import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { fetchAgents, deleteAgent as deleteAgentApi, fetchTemplates } from '../lib/fetchers';
import { useGeneralStore } from '@/providers/general-store-provider';
import { IAgent } from '@/types/agent';
import { useState } from 'react';

export function useAgentQueries() {
  const setAgents = useGeneralStore(state => state.setAgents);
  const agents = useGeneralStore(state => state.agents);
  const setCurrentAgentId = useGeneralStore(state => state.setCurrentAgentId);
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | null>(null);

  // Agents Query
  const agentsFromStore = agents;
  const {
    data: agentsData,
    error: queryError,
    isLoading,
    isFetching
  } = useQuery({
    queryKey: ['agents'],
    queryFn: fetchAgents,
    initialData: agentsFromStore.length > 0 ? agentsFromStore : undefined,
  });


  // Templates Query
  const { data: templates } = useQuery({
    queryKey: ['templates'],
    queryFn: fetchTemplates,
  });

  // Delete Agent Mutation
  const deleteAgentMutation = useMutation({
    mutationFn: deleteAgentApi,
    onSuccess: (_, agentId) => {
      const latestAgents = agents.filter((agent: IAgent) => agent.id !== agentId);
      const getLastAgent = latestAgents[latestAgents.length - 1];
      if (getLastAgent && latestAgents.length > 0) {
        setCurrentAgentId(getLastAgent.id);
      }
      setAgents(latestAgents || []);
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      setError(null);
    },
    onError: (error: any) => {
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      setError(error?.detail || 'Failed to delete agent. Please try again.');
    }
  });

  return {
    agents,
    agentsData,
    queryError,
    isLoading,
    isFetching,
    templates,
    deleteAgentMutation,
    error,
    setError,
    setCurrentAgentId,
    setAgents,
  };
} 