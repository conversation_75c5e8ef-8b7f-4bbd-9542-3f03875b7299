import axiosInstanceScenarios from '@/utils/axiosInstanceScenarios';

export const fetchAgents = async () => {
  const res = await axiosInstanceScenarios.get('/agents');
  return res.data;
};

export const fetchTemplates = async () => {
  const res = await axiosInstanceScenarios.get('/agents/templates');
  return res.data;
};

export const createAgent = async (agentData: any) => {
  const res = await axiosInstanceScenarios.post('/agents', agentData);
  return res.data;
};

export const updateAgent = async ({ agentId, agentData }: { agentId: string, agentData: any }) => {
  const res = await axiosInstanceScenarios.put(`/agents/${agentId}`, agentData);
  return res.data;
};

export const deleteAgent = async (agentId: string) => {
  await axiosInstanceScenarios.delete(`/agents/${agentId}`);
  return agentId;
}; 