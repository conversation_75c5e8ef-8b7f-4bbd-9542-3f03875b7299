@import '@/styles/variables.module';

.modalRoot {

  :global {
    .ant-modal-wrap {
      padding: 50px ;
    }

    .ant-modal-close {
      top: 24px !important;
    }

    .ant-form-item-label {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }

    textarea.ant-input {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      padding: 12px;
    }

    .ant-select-selection-placeholder {
      color: #505050 !important;
    }
  }

  .labelSubtitle {
    color: $gray;
    font-weight: 400;
  }

  .infoIcon {
    color: #007AFF;
    font-size: 24px;
  }

  .examples {
    margin-bottom: 24px;
    margin-top: -12px;
  }
}

.bx {
  font-size: 24px;
  cursor: pointer;
  color: $gray;
}

.numberInput {
  width: 100% !important;
}

.createButton {
  width: 100%;
  height: 40px !important;
  padding: 8px 20px !important;
  border-radius: 4px !important;
}

.select {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;

  .selectIcon {
    font-size: 24px;
    color: $black;
  }
}

.select {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;

  .selectIcon {
    font-size: 24px;
    color: $black;
  }

  :global {
    .ant-select-clear {
      width: 24px !important;
      height: 24px !important;
      margin-top: -12px !important;

      .anticon-close-circle {
        width: 24px;
        height: 24px;
        font-size: 24px;
      }
    }

  }
}

.customButton {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: "Plus Jakarta Sans";
  font-size: 14px;
  font-weight: 500;
  color: #7F56D9;
  border-color: #7F56D9;
  
  &:hover {
    color: #6941C6;
    border-color: #6941C6;
    background-color: #F9F5FF;
  }

  .MuiSvgIcon-root {
    font-size: 20px;
  }
}