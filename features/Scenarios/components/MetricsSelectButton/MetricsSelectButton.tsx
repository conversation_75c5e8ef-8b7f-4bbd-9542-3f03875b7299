import MetricsModal from "@/features/Metrics/components/MetricsModal/MetricsModal";
import { IMetric } from "@/types/metric";
import { Box, Button, MenuItem, OutlinedInput } from "@mui/material";


interface MetricsSelectButtonProps {
    metrics: IMetric[];
    selectedMetrics: string[];
    isMetricsModalOpen: boolean;
    MUISelect: any;
    Chip: any;
    CustomDropdownIcon: any;
    fetchMetrics: () => void;
    setSelectedMetrics: (metrics: string[]) => void;
    setIsMetricsModalOpen: (open: boolean) => void;
}

export const renderMetricsSelect = ({ metrics, selectedMetrics, setSelectedMetrics, setIsMetricsModalOpen, isMetricsModalOpen, fetchMetrics, MUISelect, Chip, CustomDropdownIcon }: MetricsSelectButtonProps) => (
    metrics.length === 0 ? (
        <>
            <Button
                sx={{
                    borderRadius: '16px',
                    fontWeight: 600,
                    fontFamily: 'Plus Jakarta Sans !important',
                    fontSize: 16,
                    height: '56px',
                    textTransform: 'none',
                    border: '1px solid #E0E0E0',
                    color: '#1B1B1B',
                    width: '100%',
                    mt: 1,
                }}
                onClick={() => setIsMetricsModalOpen(true)}
            >
                Create Metric
            </Button>
            <MetricsModal
                isModalOpen={isMetricsModalOpen}
                onCancel={() => setIsMetricsModalOpen(false)}
                initialData={null}
                onMetricCreated={() => {
                    setIsMetricsModalOpen(false);
                    fetchMetrics();
                }}
            />
        </>
    ) : (
        <Box sx={{ position: 'relative' }}>
            <MUISelect
                multiple
                value={selectedMetrics}
                onChange={(e: React.ChangeEvent<{ value: unknown }>) => setSelectedMetrics(typeof e.target.value === 'string' ? e.target.value.split(',') : e.target.value as string[])}
                input={<OutlinedInput />}
                renderValue={(selected: string[]) => (
                    <Box sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 0.5,
                        width: '100%',
                        padding: '6px 8px',
                    }}>
                        {(selected as string[]).map((value) => (
                            <Chip
                                key={value}
                                label={metrics.find(m => m.id === value)?.name || value}
                                sx={{
                                    background: '#F5F5F5',
                                    color: '#1B1B1B',
                                    fontWeight: 600,
                                    fontFamily: 'Plus Jakarta Sans',
                                    borderRadius: '999px',
                                    paddingX: 1.5,
                                    paddingY: 0.25,
                                    fontSize: 14,
                                    marginRight: 0.75,
                                    marginBottom: 0.75,
                                    '& .MuiChip-deleteIcon': {
                                        color: '#666666',
                                        marginRight: '2px',
                                        marginLeft: '-4px',
                                        '&:hover': {
                                            color: '#000000',
                                        },
                                    },
                                }}
                                onMouseDown={(e: React.MouseEvent) => e.stopPropagation()}
                                onDelete={() => {
                                    const newMetrics = selectedMetrics.filter(m => m !== value);
                                    setSelectedMetrics(newMetrics);
                                }}
                            />
                        ))}
                    </Box>
                )}
                required
                displayEmpty
                fullWidth
                sx={{
                    fontFamily: 'Plus Jakarta Sans',
                    borderRadius: '16px',
                    fontSize: 16,
                    fontWeight: 600,
                    background: '#FFFFFF',
                    border: '1px solid #E0E0E0',
                    '& .MuiOutlinedInput-root': {
                        borderRadius: '16px',
                    },
                    '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#E0E0E0',
                        borderWidth: '1px',
                    },
                    '& .MuiSelect-icon': {
                        display: 'none', // Hide the default icon
                    },
                    '& .MuiBox-root': {
                        padding: '8px',
                    },
                }}
            >
                <MenuItem value="" disabled>Select metrics</MenuItem>
                {metrics.map(metric => (
                    <MenuItem key={metric.id} value={metric.id} sx={{ fontFamily: 'Plus Jakarta Sans', fontWeight: 600, fontSize: 15 }}>
                        {metric.name}
                    </MenuItem>
                ))}
            </MUISelect>
            <CustomDropdownIcon />
        </Box>
    )
);