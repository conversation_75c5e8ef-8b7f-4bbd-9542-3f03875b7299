@import "@/styles/variables.module";

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.title {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 40px; /* 166.667% */
  color: #191a1d;
  font-feature-settings: "liga" off;
}

.emptyStateCard {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 48px;
  background-color: #ffffff;
  border-radius: 24px;
}

.emptyStateContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 18px;
}

.iconContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  background-color: #f9f6ff;
  border-radius: 24px;
}

.textContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.emptyStateTitle {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 800;
  font-size: 24px;
  line-height: 1.58;
  text-align: center;
  color: #191a1d;
  margin: 0;
}

.emptyStateDescription {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5;
  text-align: center;
  color: rgba(27, 27, 27, 0.8);
  margin: 0;
}

.newPersonalityButton {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 14px 40px;
  background-color: #7f56d9 !important;
  border-radius: 16px !important;
  font-family: "Plus Jakarta Sans", sans-serif !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  line-height: 1.71 !important;
  color: #ffffff !important;
  text-transform: none !important;
  margin-top: 6px !important;

  &:hover {
    background-color: #6941c6 !important;
  }
}
