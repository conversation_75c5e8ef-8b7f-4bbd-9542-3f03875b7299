import React from 'react';
import styles from './ProgressIndicator.module.scss';
import cn from 'classnames';

interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
}) => {
  return (
    <div className={styles.progressContainer}>
      {Array.from({ length: totalSteps }).map((_, index) => (
        <div 
          key={index} 
          className={cn(
            styles.progressDot,
            index + 1 === currentStep && styles.active,
            index + 1 < currentStep && styles.completed
          )}
        />
      ))}
    </div>
  );
};

export default ProgressIndicator;
