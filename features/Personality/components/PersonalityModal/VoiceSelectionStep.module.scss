@import "@/styles/variables.module";

.container {
  width: 100%;
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

.form {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  min-height: 600px;
  gap: 20px;
}

.contentSection {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
}

.formSection {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.formItem {
  margin-bottom: 0;
}

.labelContainer {
  display: flex;
  align-items: center;
  gap: 4px;
}

.label {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.43;
  color: #1b1b1b;
}

.infoIconContainer {
  width: 16px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.infoIcon {
  cursor: pointer;
}

.sliderLabelContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sliderLabel {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1b1b1b;
}

.sliderValue {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #915eff;
}

.sliderContainer {
  position: relative;
  width: 100%;
  height: 30px;
  margin-bottom: 16px;
  cursor: pointer;
}

.sliderBackground {
  position: absolute;
  top: 13px;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: #ebeced;
  border-radius: 4px;
}

.sliderProgress {
  position: absolute;
  top: 13px;
  left: 0;
  height: 4px;
  background-color: #915eff;
  border-radius: 4px;
}

.sliderHandle {
  position: absolute;
  top: 7px;
  transform: translateX(-50%);
  width: 16px;
  height: 16px;
  background-color: white;
  border: 2px solid #915eff;
  border-radius: 50%;
  box-shadow:
    0px 1px 2px -1px rgba(16, 24, 40, 0.06),
    0px 2px 4px -1px rgba(16, 24, 40, 0.1);
  cursor: grab;

  &:hover {
    transform: translateX(-50%) scale(1.1);
  }

  &:active {
    cursor: grabbing;
  }
}

.voiceOptions {
  display: flex;
  gap: 12px;
  width: 100%;
}

.voiceOption {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 14px;
  border-radius: 24px;
  border: 1px solid #e3e3e3;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    border-color: #915eff;
  }

  &.selected {
    border-color: #915eff;
    box-shadow: 0px 0px 12px 0px rgba(127, 86, 217, 0.3);
  }
}

.voiceOptionContent {
  display: flex;
  align-items: center;
  gap: 8px;
}

.playIcon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #0f172a;
  font-size: 12px;
}

.voiceText {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.71;
  color: #1b1b1b;
}

.checkIcon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #915eff;
  font-size: 16px;
  font-weight: bold;
}

.toggleContainer {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 4px;
  border-radius: 200px;
  border: 1px solid #ededed;
  width: 144px;
}

.toggleOption {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 10px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;

  span {
    font-family: "Inter", sans-serif;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
    color: rgba(25, 26, 29, 0.8);
  }

  &.active {
    background-color: #9e77ed;

    span {
      color: #ffffff;
    }
  }
}

.noiseSelect {
  width: 100%;
  margin-top: 16px;
  :global {
    .MuiOutlinedInput-root {
      background-color: #ffffff;
      border-radius: 16px;
      padding: 12px 14px;
      border: 1px solid #e3e3e3;
      height: 48px;

      &:hover .MuiOutlinedInput-notchedOutline {
        border-color: #7f56d9;
      }

      &.Mui-focused .MuiOutlinedInput-notchedOutline {
        border-color: #7f56d9;
        border-width: 1px;
      }
    }

    .MuiOutlinedInput-notchedOutline {
      border: none;
      // This removes the line above the select
      legend {
        display: none;
      }
    }

    .MuiSelect-select {
      padding: 0 !important;
      min-height: auto !important;
      display: flex;
      align-items: center;
    }

    .MuiSelect-icon {
      color: #101828;
      font-size: 20px;
      right: 14px;
    }

    .MuiMenuItem-root {
      padding: 8px 16px;
      font-family: "Plus Jakarta Sans", sans-serif;
      font-size: 14px;
      font-weight: 600;

      &:hover {
        background-color: #f9f5ff;
      }

      &.Mui-selected {
        background-color: #f9f5ff;
        color: #7f56d9;

        &:hover {
          background-color: #f4ebff;
        }
      }
    }
  }
}

.footerSection {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  margin-top: auto;
}

.buttonContainer {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  width: 100%;
}

.backButton {
  flex: 1;
  height: 64px;
  background-color: #f6f6f6 !important;
  border: none !important;
  border-radius: 16px !important;
  font-family: "Plus Jakarta Sans", sans-serif !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
  color: #595959 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: none !important;
  text-transform: none !important;

  &:hover {
    background-color: #f0f0f2 !important;
    color: #595959 !important;
  }
}

.backIcon {
  font-size: 24px !important;
  color: #444 !important;
}

.finishButton {
  flex: 1;
  height: 64px;
  background-color: #8b5cf6 !important;
  border: none !important;
  border-radius: 16px !important;
  font-family: "Plus Jakarta Sans", sans-serif !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
  color: #ffffff !important;
  box-shadow: none !important;
  text-transform: none !important;

  &:hover {
    background-color: #7c3aed !important;
    color: #ffffff !important;
  }
}
