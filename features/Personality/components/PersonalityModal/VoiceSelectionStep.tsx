import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Row, Col } from "antd";
import Image from "next/image";
import styles from "./VoiceSelectionStep.module.scss";
import cn from "classnames";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { Select, MenuItem, Typography, FormControl } from "@mui/material";

interface VoiceSelectionStepProps {
  onBack: () => void;
  onFinish: (values: {
    voice: string;
    voiceSpeed: number;
    backgroundNoise: boolean;
    noiseType: string;
  }) => void;
  initialValues?: {
    voice: string;
    voiceSpeed: number;
    backgroundNoise: boolean;
    noiseType: string;
  };
}

const VoiceSelectionStep: React.FC<VoiceSelectionStepProps> = ({
  onBack,
  onFinish,
  initialValues = {
    voice: "voice-2",
    voiceSpeed: 50,
    backgroundNoise: true,
    noiseType: "",
  },
}) => {
  const [form] = Form.useForm();
  const [selectedVoice, setSelectedVoice] = useState(initialValues.voice);
  const [backgroundNoise, setBackgroundNoise] = useState(
    initialValues.backgroundNoise,
  );
  const [voiceSpeed, setVoiceSpeed] = useState(initialValues.voiceSpeed);

  const handleVoiceSelect = (voice: string) => {
    setSelectedVoice(voice);
    form.setFieldsValue({ voice });
  };

  const handleVoiceSpeedChange = (value: number) => {
    setVoiceSpeed(value);
    form.setFieldsValue({ voiceSpeed: value });
  };

  const handleBackgroundNoiseChange = (value: boolean) => {
    setBackgroundNoise(value);
    form.setFieldsValue({ backgroundNoise: value });

    // If background noise is turned off, clear the noise type
    if (!value) {
      form.setFieldsValue({ noiseType: "" });
    }
  };

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      onFinish(values);
    });
  };

  const playVoiceSample = (voice: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the parent click event
    console.log(`Playing voice sample for ${voice}`);
    // In a real implementation, this would play an audio sample
  };

  return (
    <div className={styles.container}>
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        className={styles.form}
      >
        <div className={styles.contentSection}>
          {/* Voice Section */}
          <div className={styles.formSection}>
            <Form.Item
              label={
                <div className={styles.labelContainer}>
                  <span className={styles.label}>Voice</span>
                  <div className={styles.infoIconContainer}>
                    <Image
                      src="/images/exclamation-circle.svg"
                      alt="Info"
                      width={16}
                      height={16}
                      className={styles.infoIcon}
                    />
                  </div>
                </div>
              }
              name="voice"
              className={styles.formItem}
            >
              <div
                className={styles.sliderContainer}
                onClick={(e) => {
                  const container = e.currentTarget;
                  const rect = container.getBoundingClientRect();
                  const newPosition = Math.max(
                    0,
                    Math.min(100, ((e.clientX - rect.left) / rect.width) * 100),
                  );
                  handleVoiceSpeedChange(Math.round(newPosition));
                }}
              >
                <div className={styles.sliderBackground}></div>
                <div
                  className={styles.sliderProgress}
                  style={{ width: `${voiceSpeed}%` }}
                ></div>
                <div
                  className={styles.sliderHandle}
                  style={{ left: `${voiceSpeed}%` }}
                  onMouseDown={(e) => {
                    e.stopPropagation(); // Prevent triggering the parent click event

                    // Store the container reference before the event handlers
                    const sliderContainer = e.currentTarget.parentElement;
                    if (!sliderContainer) return;

                    const containerRect =
                      sliderContainer.getBoundingClientRect();

                    const handleDrag = (moveEvent: MouseEvent) => {
                      const newPosition = Math.max(
                        0,
                        Math.min(
                          100,
                          ((moveEvent.clientX - containerRect.left) /
                            containerRect.width) *
                            100,
                        ),
                      );
                      handleVoiceSpeedChange(Math.round(newPosition));
                    };

                    const handleMouseUp = () => {
                      document.removeEventListener("mousemove", handleDrag);
                      document.removeEventListener("mouseup", handleMouseUp);
                    };

                    document.addEventListener("mousemove", handleDrag);
                    document.addEventListener("mouseup", handleMouseUp);
                  }}
                ></div>
              </div>

              {/* Voice Options */}
              <div className={styles.voiceOptions}>
                <div
                  className={cn(
                    styles.voiceOption,
                    selectedVoice === "voice-1" && styles.selected,
                  )}
                  onClick={() => handleVoiceSelect("voice-1")}
                >
                  <div className={styles.voiceOptionContent}>
                    <div
                      className={styles.playIcon}
                      onClick={(e) => playVoiceSample("voice-1", e)}
                    >
                      ▶
                    </div>
                    <div className={styles.voiceText}>Voice -1</div>
                  </div>
                </div>

                <div
                  className={cn(
                    styles.voiceOption,
                    selectedVoice === "voice-2" && styles.selected,
                  )}
                  onClick={() => handleVoiceSelect("voice-2")}
                >
                  <div className={styles.voiceOptionContent}>
                    <div
                      className={styles.playIcon}
                      onClick={(e) => playVoiceSample("voice-2", e)}
                    >
                      ▶
                    </div>
                    <div className={styles.voiceText}>Voice -2</div>
                  </div>
                  {selectedVoice === "voice-2" && (
                    <div className={styles.checkIcon}>✓</div>
                  )}
                </div>

                <div
                  className={cn(
                    styles.voiceOption,
                    selectedVoice === "voice-3" && styles.selected,
                  )}
                  onClick={() => handleVoiceSelect("voice-3")}
                >
                  <div className={styles.voiceOptionContent}>
                    <div
                      className={styles.playIcon}
                      onClick={(e) => playVoiceSample("voice-3", e)}
                    >
                      ▶
                    </div>
                    <div className={styles.voiceText}>Voice -3</div>
                  </div>
                </div>
              </div>
            </Form.Item>
          </div>

          {/* Background Noise Section */}
          <div className={styles.formSection}>
            <Form.Item name="backgroundNoise" className={styles.formItem}>
              <Row align="middle" justify="space-between">
                <Col span={8}>
                  <div className={styles.labelContainer}>
                    <span className={styles.label}>Background noise</span>
                    <div className={styles.infoIconContainer}>
                      <Image
                        src="/images/exclamation-circle.svg"
                        alt="Info"
                        width={16}
                        height={16}
                        className={styles.infoIcon}
                      />
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles.toggleContainer}>
                    <div
                      className={cn(
                        styles.toggleOption,
                        backgroundNoise && styles.active,
                      )}
                      onClick={() => handleBackgroundNoiseChange(true)}
                    >
                      <span>Yes</span>
                    </div>
                    <div
                      className={cn(
                        styles.toggleOption,
                        !backgroundNoise && styles.active,
                      )}
                      onClick={() => handleBackgroundNoiseChange(false)}
                    >
                      <span>No</span>
                    </div>
                  </div>
                </Col>
              </Row>
            </Form.Item>

            {backgroundNoise && (
              <Form.Item name="noiseType" className={styles.formItem}>
                <div className={styles.noiseSelect}>
                  <FormControl fullWidth>
                    <Select
                      value={form.getFieldValue("noiseType") || ""}
                      onChange={(e) => {
                        form.setFieldsValue({ noiseType: e.target.value });
                      }}
                      displayEmpty
                      sx={{
                        fontFamily: "Plus Jakarta Sans",
                        borderRadius: "16px",
                        fontSize: 14,
                        fontWeight: 500,
                        height: "48px",
                        "& .MuiOutlinedInput-root": {
                          borderRadius: "16px",
                          fontSize: 14,
                          fontWeight: 500,
                        },
                      }}
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            borderRadius: "12px",
                            mt: 1,
                            boxShadow:
                              "0px 4px 6px -1px rgba(0, 0, 0, 0.1), 0px 2px 4px -1px rgba(0, 0, 0, 0.06)",
                          },
                        },
                      }}
                      renderValue={(selected) => (
                        <Typography
                          sx={{
                            fontWeight: 500,
                            fontSize: 14,
                            color: selected ? "#101828" : "#667085",
                            fontFamily: "Plus Jakarta Sans",
                            lineHeight: 1.7142857142857142,
                          }}
                        >
                          {selected || "Select Noise"}
                        </Typography>
                      )}
                    >
                      <MenuItem
                        value=""
                        disabled
                        sx={{
                          fontSize: 14,
                          fontWeight: 500,
                          fontFamily: "Plus Jakarta Sans",
                          padding: "12px 14px",
                        }}
                      >
                        Select Noise
                      </MenuItem>
                      <MenuItem
                        value="office"
                        sx={{
                          fontSize: 14,
                          fontWeight: 500,
                          fontFamily: "Plus Jakarta Sans",
                          padding: "12px 14px",
                        }}
                      >
                        Office
                      </MenuItem>
                      <MenuItem
                        value="cafe"
                        sx={{
                          fontSize: 14,
                          fontWeight: 500,
                          fontFamily: "Plus Jakarta Sans",
                          padding: "12px 14px",
                        }}
                      >
                        Cafe
                      </MenuItem>
                      <MenuItem
                        value="street"
                        sx={{
                          fontSize: 14,
                          fontWeight: 500,
                          fontFamily: "Plus Jakarta Sans",
                          padding: "12px 14px",
                        }}
                      >
                        Street
                      </MenuItem>
                      <MenuItem
                        value="nature"
                        sx={{
                          fontSize: 14,
                          fontWeight: 500,
                          fontFamily: "Plus Jakarta Sans",
                          padding: "12px 14px",
                        }}
                      >
                        Nature
                      </MenuItem>
                    </Select>
                  </FormControl>
                </div>
              </Form.Item>
            )}
          </div>

          {/* Spacer to push content to the top and buttons to the bottom */}
          <div style={{ flex: 1 }}></div>
        </div>

        {/* Footer Section */}
        <div className={styles.footerSection}>
          <div className={styles.buttonContainer}>
            <Button
              className={styles.backButton}
              onClick={onBack}
              icon={<ArrowBackIcon className={styles.backIcon} />}
            >
              Go back
            </Button>
            <Button
              className={styles.finishButton}
              onClick={handleSubmit}
              type="primary"
            >
              Add Personality
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default VoiceSelectionStep;
