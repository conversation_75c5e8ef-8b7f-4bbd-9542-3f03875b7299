import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Tooltip } from "antd";
import Image from "next/image";
import styles from "./GeneratePersonalityStep.module.scss";
import TextArea from "antd/es/input/TextArea";
import cn from "classnames";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";

interface GeneratePersonalityStepProps {
  onBack: () => void;
  onGenerate: (values: { count: number; instructions: string }) => void;
  initialValues?: { count?: number; instructions: string };
}

const GeneratePersonalityStep: React.FC<GeneratePersonalityStepProps> = ({
  onBack,
  onGenerate,
  initialValues = { count: 4, instructions: "" },
}) => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(initialValues.count || 4);
  const [instructionLength, setInstructionLength] = useState(
    initialValues.instructions.length,
  );
  const maxInstructionLength = 1000;

  const handleInstructionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    setInstructionLength(e.target.value.length);
  };

  const handleAutoGenerate = () => {
    form.setFieldsValue({
      instructions:
        "Generate personalities with different characteristics such as age, gender, and background. Each personality should have a unique communication style and tone.",
    });
    setInstructionLength(form.getFieldValue("instructions").length);
  };

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      onGenerate({ ...values, count });
    });
  };

  const decrementCount = () => {
    if (count > 1) {
      setCount(count - 1);
    }
  };

  const incrementCount = () => {
    if (count < 10) {
      setCount(count + 1);
    }
  };

  return (
    <div className={styles.container}>
      <Form
        form={form}
        layout="vertical"
        initialValues={{ ...initialValues, count }}
        className={styles.form}
      >
        <div className={styles.countContainer}>
          <div className={styles.labelContainer}>
            <span className={styles.label}>Number of Personalites</span>
            <Tooltip title="Choose how many personalities to generate">
              <Image
                src="/images/exclamation-circle.svg"
                alt="Info"
                width={16}
                height={16}
                className={styles.infoIcon}
              />
            </Tooltip>
          </div>

          <div className={styles.counterContainer}>
            <div
              className={cn(
                styles.counterButton,
                count <= 1 && styles.disabled,
              )}
              onClick={decrementCount}
            >
              <Image
                src="/images/minus-icon.svg"
                alt="Decrease"
                width={16}
                height={16}
              />
            </div>

            <div className={styles.countDisplay}>
              {count.toString().padStart(2, "0")}
            </div>

            <div
              className={cn(
                styles.counterButton,
                count >= 10 && styles.disabled,
              )}
              onClick={incrementCount}
            >
              <Image
                src="/images/plus-icon.svg"
                alt="Increase"
                width={16}
                height={16}
              />
            </div>
          </div>
        </div>

        <div className={styles.instructionsHeader}>
          <div className={styles.labelContainer}>
            <span className={styles.label}>Instructions</span>
            <Tooltip title="Provide instructions for the personalities">
              <Image
                src="/images/exclamation-circle.svg"
                alt="Info"
                width={16}
                height={16}
                className={styles.infoIcon}
              />
            </Tooltip>
          </div>
          <div
            className={styles.autoGenerateButton}
            onClick={handleAutoGenerate}
          >
            <Image
              src="/images/sparkles.svg"
              alt="Auto Generate"
              width={16}
              height={16}
            />
            <span>Auto-Generate</span>
          </div>
        </div>

        <Form.Item name="instructions" className={styles.textAreaContainer}>
          <TextArea
            placeholder="Definition: How well does the support bot stick to the context of the conversation and understand the user's intent?"
            className={styles.textArea}
            onChange={handleInstructionChange}
            autoSize={{ minRows: 8, maxRows: 12 }}
          />
        </Form.Item>

        <div className={styles.textAreaFooter}>
          <span className={styles.charCount}>
            {instructionLength}/{maxInstructionLength}
          </span>
          <div className={styles.creditsContainer}>
            <div className={styles.progressCircle}>
              <svg width="16" height="16" viewBox="0 0 16 16">
                <circle
                  cx="8"
                  cy="8"
                  r="6"
                  stroke="#D1D5DB"
                  strokeWidth="2.5"
                  fill="none"
                />
                <circle
                  cx="8"
                  cy="8"
                  r="6"
                  stroke="#000000"
                  strokeWidth="2.5"
                  fill="none"
                  strokeDasharray={`${Math.min(instructionLength / maxInstructionLength, 1) * 37.7} 37.7`}
                  strokeDashoffset="9.425"
                  transform="rotate(-90 8 8)"
                />
              </svg>
            </div>
            <span className={styles.credits}>9,652 credits remaining</span>
          </div>
        </div>

        <div className={styles.buttonContainer}>
          <Button
            className={styles.backButton}
            onClick={onBack}
            icon={<ArrowBackIcon className={styles.backIcon} />}
          >
            Go back
          </Button>
          <Button
            className={styles.generateButton}
            onClick={handleSubmit}
            type="primary"
          >
            Generate Personality
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default GeneratePersonalityStep;
