@import "@/styles/variables.module";

.container {
  width: 100%;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.formItem {
  margin-bottom: 0;
}

.input {
  border-radius: 14px;
  padding: 10px 14px;
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.71;
  color: #1b1b1b;
  border: 1px solid #e3e3e3;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);

  &:focus,
  &:hover {
    border-color: #7f56d9;
    box-shadow: 0px 0px 12px 0px rgba(127, 86, 217, 0.3);
  }
}

.instructionsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 6px;
}

.labelContainer {
  display: flex;
  align-items: center;
  gap: 4px;
}

.label {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.43;
  color: #1b1b1b;
}

.infoIcon {
  cursor: pointer;
}

.autoGenerateButton {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 16px 8px 8px;
  background-color: #ffffff;
  border: 1px solid #e3e3e3;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  span {
    font-family: "Plus Jakarta Sans", sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.43;
    color: #1b1b1b;
  }

  &:hover {
    background-color: #f9f9f9;
  }
}

.textAreaContainer {
  margin-bottom: 0;
}

.textArea {
  border-radius: 14px;
  border: 1px solid #e3e3e3;
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.71;
  color: #1b1b1b;
  resize: none;
  height: 300px;

  &:focus,
  &:hover {
    border-color: #7f56d9;
    box-shadow: 0px 0px 12px 0px rgba(127, 86, 217, 0.3);
  }
}

.textAreaFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #e3e3e3;
  margin-top: -1px;
  border-radius: 0 0 14px 14px;
  background-color: #ffffff;
}

.charCount {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.5;
  color: #667085;
}

.creditsContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progressCircle {
  display: flex;
  align-items: center;
  justify-content: center;
}

.credits {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.5;
  color: #667085;
}

.buttonContainer {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 20px;
}

.backButton {
  flex: 1;
  height: 64px !important;
  background-color: #f6f6f6 !important;
  border: none !important;
  border-radius: 16px !important;
  font-family: "Plus Jakarta Sans", sans-serif !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
  color: #595959 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: none !important;
  text-transform: none !important;

  &:hover {
    background-color: #f0f0f2 !important;
    color: #595959 !important;
  }
}

.backIcon {
  font-size: 24px !important;
  color: #444 !important;
}

.nextButton {
  flex: 1;
  height: 64px !important;
  background-color: #8b5cf6 !important;
  border: none !important;
  border-radius: 16px !important;
  font-family: "Plus Jakarta Sans", sans-serif !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
  color: #ffffff !important;
  box-shadow: none !important;
  text-transform: none !important;

  &:hover {
    background-color: #7c3aed !important;
    color: #ffffff !important;
  }
}

.nextButtonDefault {
  background-color: rgba(139, 92, 246, 0.5) !important;
}
