import React, { useState, useEffect } from "react";
import { Box, TextField, Button, Typography, FormControl, InputAdornment, Select, MenuItem, CircularProgress } from "@mui/material";
import Image from "next/image";

interface CreatePersonalityStepProps {
  onBack: () => void;
  onNext: (values: { name: string; language: string; description: string, language_id: number }) => void;
  initialValues?: { name: string; language?: string; description: string, language_id?: number };
  editMode?: boolean;
  languages: any[];
  loading?: boolean;
}

const CreatePersonalityStep: React.FC<CreatePersonalityStepProps> = ({
  onBack,
  onNext,
  initialValues = { name: "", language: "en", description: "" },
  editMode = false,
  languages = [],
  loading = false,
}) => {
  const [name, setName] = useState(initialValues.name);
  const [language, setLanguage] = useState(initialValues.language || "en");
  const [description, setDescription] = useState(initialValues.description);
  const [nameError, setNameError] = useState("");
  const [languageError, setLanguageError] = useState("");
  const [descriptionError, setDescriptionError] = useState("");

  // Sync local state with initialValues when they change (for edit mode)
  useEffect(() => {
    setName(initialValues.name);
    setLanguage(initialValues.language || "en");
    setDescription(initialValues.description);
  }, [initialValues]);

  // Set default language to English if available
  useEffect(() => {
    if (!initialValues.language && languages.length > 0) {
      const english = languages.find((l: any) => l.code === 'en' || l.name?.toLowerCase() === 'english');
      if (english) setLanguage(english.code || english.id);
      else setLanguage(languages[0].code || languages[0].id);
    }
  }, [languages, initialValues.language]);

  const handleSubmit = () => {
    let valid = true;
    if (!name.trim()) {
      setNameError("Personality name is required");
      valid = false;
    } else {
      setNameError("");
    }
    if (!language) {
      setLanguageError("Language is required");
      valid = false;
    } else {
      setLanguageError("");
    }
    if (!description.trim()) {
      setDescriptionError("Description is required");
      valid = false;
    } else {
      setDescriptionError("");
    }
    if (!valid) return;
    onNext({
      name, language, description,
      language_id: 0
    });
  };

  return (
    <Box component="form" sx={{ mt: 1 }}>
      {/* Name */}
      <FormControl fullWidth sx={{ mb: 2 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
          <Typography
            variant="subtitle1"
            sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
          >
            Personality Name*
          </Typography>
        </Box>
        <TextField
          required
          value={name}
          placeholder="Add Name"
          autoComplete="off"
          onChange={e => setName(e.target.value)}
          error={!!nameError}
          helperText={nameError}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Box sx={{
                  width: 28,
                  height: 28,
                  borderRadius: '50%',
                  background: '#F6F6F6',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mr: 1,
                }}>
                  <Image src="/images/personality-icon.svg" alt="Personality" width={18} height={18} />
                </Box>
              </InputAdornment>
            ),
          }}
          sx={{
            fontFamily: "Plus Jakarta Sans",
            borderRadius: '16px',
            '& .MuiOutlinedInput-root': {
              borderRadius: '16px',
              fontSize: 18,
              fontWeight: 500,
              '& input::placeholder': {
                color: '#BDBDBD',
                opacity: 1,
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#7F56D9',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#7F56D9',
              },
            },
          }}
        />
      </FormControl>
      {/* Language */}
      <FormControl fullWidth sx={{ mb: 2 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
          <Typography
            sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
          >
            Language*
          </Typography>
        </Box>
        {loading && languages.length === 0 ? (
          <Box sx={{ display: 'flex', alignItems: 'center', minHeight: 56 }}>
            <CircularProgress size={24} sx={{ mr: 2 }} />
            <Typography>Loading languages...</Typography>
          </Box>
        ) : (
          <Select
            value={language}
            onChange={e => setLanguage(e.target.value)}
            displayEmpty
            required
            error={!!languageError}
            sx={{
              fontFamily: "Plus Jakarta Sans",
              borderRadius: '16px',
              fontSize: 14,
              fontWeight: 600,
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#7F56D9',
              },
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#E0E0E0',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#7F56D9',
              },
            }}
          >
            {languages.filter((lang: any) => lang.is_active).length === 0 ? (
              <MenuItem disabled>No active languages available</MenuItem>
            ) : (
              languages.filter((lang: any) => lang.is_active).map((lang: any) => (
                <MenuItem key={lang.code} value={lang.code} sx={{ fontSize: 14, fontWeight: 600, fontFamily: 'Plus Jakarta Sans' }}>{lang.name}</MenuItem>
              ))
            )}
          </Select>
        )}
        {languageError && (
          <Typography variant="caption" color="error" sx={{ fontFamily: "Plus Jakarta Sans" }}>
            {languageError}
          </Typography>
        )}
      </FormControl>
      {/* Description */}
      <FormControl fullWidth sx={{ mb: 2 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
          <Typography
            sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
          >
            Description*
          </Typography>
        </Box>
        <TextField
          required
          multiline
          autoComplete="off"
          placeholder="Describe the personality..."
          rows={6}
          value={description}
          onChange={e => setDescription(e.target.value)}
          error={!!descriptionError}
          helperText={descriptionError}
          sx={{
            fontFamily: "Plus Jakarta Sans",
            "& .MuiOutlinedInput-root": {
              borderRadius: "12px",
              "&:hover .MuiOutlinedInput-notchedOutline": {
                borderColor: "#7F56D9",
              },
              "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderColor: "#7F56D9",
              },
            },
          }}
        />
      </FormControl>
      {/* Actions */}
      <Box sx={{ mt: 2, mb: 2, width: '100%' }}>
        <Button
          onClick={handleSubmit}
          variant="contained"
          fullWidth
          sx={{
            backgroundColor: '#8B5CF6',
            borderRadius: '16px',
            fontWeight: 600,
            fontSize: 16,
            color: '#fff',
            height: '64px',
            width: '100%',
            px: 5,
            boxShadow: 'none',
            fontFamily: "Plus Jakarta Sans",
            textTransform: 'none',
            minWidth: 0,
            '&:hover': { backgroundColor: '#7C3AED' },
          }}
          disabled={!name || !language || !description || loading}
        >
          {loading ? <CircularProgress size={22} sx={{ color: '#fff' }} /> : (editMode ? 'Update Personality' : 'Create Personality')}
        </Button>
      </Box>
    </Box>
  );
};

export default CreatePersonalityStep;
