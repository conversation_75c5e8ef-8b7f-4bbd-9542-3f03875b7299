// PersonalityCardList.tsx

import React, { ReactNode } from "react";
import { Box, SxProps, Theme } from "@mui/material";

/**
 * Props for PersonalityCardList.
 */
export interface PersonalityCardListProps {
  /**
   * The card elements to display (usually <PersonalityCard /> components).
   */
  children: ReactNode;
  /**
   * Optional spacing between cards (default: 3 = 24px).
   */
  spacing?: number;
  /**
   * Optional custom styles via MUI sx prop.
   */
  sx?: SxProps<Theme>;
}

/**
 * Displays a responsive grid of PersonalityCard components (3 per row on md+, 2 on sm, 1 on xs).
 */
export default function PersonalityCardList({
  children,
  spacing = 3,
  sx,
}: PersonalityCardListProps) {
  return (
    <Box
      sx={{
        display: "grid",
        gridTemplateColumns: {
          xs: "1fr",
          sm: "1fr 1fr",
          md: "1fr 1fr 1fr",
        },
        gap: (theme) => theme.spacing(spacing),
        alignItems: "stretch",
        ...sx,
      }}
      data-testid="personality-card-list"
    >
      {children}
    </Box>
  );
}