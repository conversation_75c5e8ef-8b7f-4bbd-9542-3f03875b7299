"use client";

import React, { useState } from "react";
import PersonalityCardList from "./components/PersonalityCardList";
import PersonalityCard from "./components/PersonalityCard";
import PageTransition from "@/app/ui/PageTransition/PageTransition";
import ExploreMoreButton from "@/components/ExploreMoreButton";
import TemplateHeaderSection from "@/components/TemplateHeaderSection/TemplateHeaderSection";
import { Typography } from "@mui/material";
import { Box } from "@mui/material";
import { useGetPersonalities, useDeletePersonality } from "./lib/usePersonalityQueries";
import EmptyState from "@/components/EmptyState";
import AddIcon from "@mui/icons-material/Add";
import PersonalityModal from "./components/PersonalityModal/PersonalityModal";
import PersonalityTemplatesModal from "./components/PersonalityTemplateDialog/PersonalityTemplateDialog";
import { LoadingSpinner } from "@/components/common/LoadingSpinner";
import CreateEntityCard from "@/components/CreateEntityCard/CreateEntityCard";
import { useNotification } from '@/context/NotificationContext/NotificationContext';



export default function PersonalityScreen() {
    const [hoveredIdx, setHoveredIdx] = useState<number | null>(null);

    const { data: personalitiesData, isLoading, error } = useGetPersonalities();
    const [openCreatePersonalityModal, setOpenCreatePersonalityModal] = useState(false);
    const [openPersonalityTemplatesModal, setOpenPersonalityTemplatesModal] = useState(false);
    const [editingPersonality, setEditingPersonality] = useState(null);
    const [editMode, setEditMode] = useState(false);

    
    const notify = useNotification && useNotification();
    const deletePersonalityMutation = useDeletePersonality({
        onSuccess: () => {
            notify?.success({ message: 'Personality deleted successfully!' });
        },
        onError: (error: any) => {
            notify?.error({ message: error?.response?.data?.detail || error.message || 'Failed to delete personality.' });
        }
    });
    return (

        <PageTransition>

            <Box>
                <Typography sx={{ fontWeight: 800, fontFamily: 'Plus Jakarta Sans', fontSize: '2.125rem', color: '#101828', marginBottom: '24px' }}>
                    Your Personality
                </Typography>
            </Box>

            <TemplateHeaderSection
                icon="/metrics-icon-template.svg"
                title="Personality Template"
                button={
                    <ExploreMoreButton onClick={() => setOpenPersonalityTemplatesModal(true)}>
                        Explore More
                    </ExploreMoreButton>
                }
            />
            <Box minHeight={300} display="flex" alignItems="center" justifyContent="center">
                {isLoading ? (
                    <LoadingSpinner />
                ) : (!personalitiesData?.data || personalitiesData.data.length === 0) ? (
                    <EmptyState
                        iconSrc="/task-square-empty.svg"
                        iconAlt="Empty Metrics"
                        title="No Personality Found"
                        subtitle="Start and create a new one."
                        buttonText="Add a new personality"
                        onButtonClick={() => setOpenCreatePersonalityModal(true)}
                        buttonIcon={<AddIcon sx={{ color: '#fff' }} />}
                    />
                ) : (
                    <PersonalityCardList spacing={2} sx={{
                        padding: '16px',
                        backgroundColor: '#fff',
                        borderRadius: '24px',
                    }}>
                        {personalitiesData.data.length > 0 && (
                            <div style={{ height: "100%" }}>
                                <CreateEntityCard
                                    title="Create Personality"
                                    description="Add a new personality profile for your agents"
                                    onClick={() => setOpenCreatePersonalityModal(true)}
                                />
                            </div>
                        )}
                        {personalitiesData.data.map((p: any, idx: number) => (
                            <div
                                key={p.id}
                                onMouseEnter={() => setHoveredIdx(idx)}
                                onMouseLeave={() => setHoveredIdx(null)}
                                style={{ height: "100%" }}
                            >
                                <PersonalityCard
                                    title={p.title}
                                    description={p.description}
                                    badge={p.badge}
                                    forceHover={hoveredIdx === idx}
                                    onEdit={() => {
                                        setEditingPersonality(p);
                                        setEditMode(true);
                                        setOpenCreatePersonalityModal(true);
                                    }}
                                    onDelete={() => deletePersonalityMutation.mutate(p.id)}
                                />
                            </div>
                        ))}
                    </PersonalityCardList>
                )}
            </Box>
            <PersonalityModal
                onCancel={() => {
                    setOpenCreatePersonalityModal(false);
                    setEditingPersonality(null);
                    setEditMode(false);
                }}
                isModalOpen={openCreatePersonalityModal}
                initialData={editingPersonality}
                editMode={editMode}
                onLibraryClick={() => {
                    setOpenCreatePersonalityModal(false);
                    setOpenPersonalityTemplatesModal(true);
                }}
            />
            <PersonalityTemplatesModal
                isModalOpen={openPersonalityTemplatesModal}
                onCancel={() => {
                    setOpenPersonalityTemplatesModal(false);
                    setOpenCreatePersonalityModal(false);
                }}
            />
        </PageTransition>
    );
}
