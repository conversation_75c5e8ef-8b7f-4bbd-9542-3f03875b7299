import axiosInstanceScenario from '@/utils/axiosInstanceScenarios';

export function addPersonality(data: any) {
  return axiosInstanceScenario.post('/personalities', data);
}

export function getPersonalities() {
  return axiosInstanceScenario.get('/personalities');
}

export function getPredefinedPersonalities() {
  return axiosInstanceScenario.get('/personalities/predefined');
}

export function updatePersonality(personality_id: string | number, data: any) {
  return axiosInstanceScenario.put(`/personalities/${personality_id}`, data);
}

export function deletePersonality(personality_id: string | number) {
  return axiosInstanceScenario.delete(`/personalities/${personality_id}`);
} 