import { IAddNewMetricDto, IMetricDto } from '@/app/api/types/metricDto';
import { EMetricType } from '@/types/metric';
import axiosInstanceScenarios from '@/utils/axiosInstanceScenarios';

// Fetch all metrics for an agent
export async function fetchMetrics(agentId: string): Promise<IMetricDto[]> {
    const res = await axiosInstanceScenarios.get(`/agents/${agentId}/metrics`);
    return res.data;
}

// Create a new metric for an agent
export async function createMetric(agentId: string, data: IAddNewMetricDto): Promise<IMetricDto> {
    const res = await axiosInstanceScenarios.post(`/agents/${agentId}/metrics`, data);
    return res.data;
}

// Update an existing metric for an agent
export async function updateMetric(agentId: string, metricId: string, data: IAddNewMetricDto): Promise<IMetricDto> {
    const res = await axiosInstanceScenarios.put(`/agents/${agentId}/metrics/${metricId}`, data);
    return res.data;
}

// Delete a metric for an agent
export async function deleteMetric(agentId: string, metricId: string): Promise<void> {
    await axiosInstanceScenarios.delete(`/agents/${agentId}/metrics/${metricId}`);
}

// Bulk create metrics from templates for an agent
export async function bulkCreateMetrics(agentId: string, templates: Array<{ name: string; prompt: string; type: EMetricType }>): Promise<IMetricDto[]> {
    const res = await axiosInstanceScenarios.post(`/agents/${agentId}/metrics/bulk`, templates);
    return res.data;
}

export async function fetchMetricTemplates(): Promise<IMetricDto[]> {
    const res = await axiosInstanceScenarios.get(`/metrics/templates`);
    return res.data;
}