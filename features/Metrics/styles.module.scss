@import '@/styles/variables.module';

.container {
  margin-top: 32px;
  background-color: #F9FAFB;
}

.header {
  margin-bottom: 24px;
}

.title {
  font-family: 'Plus Jakarta Sans';
  font-weight: 600;
  color: #101828;
}

.metricsGrid {
  margin-top: 24px;
}

.addButton {
  &:hover {
    background-color: #6941C6 !important;
  }
}

.exploreButton {
  &:hover {
    background-color: #F9F5FF !important;
  }
}

.bx {
  font-size: 24px;
  cursor: pointer;
  color: #667085;
}

.dropdown {
  width: 160px;

  :global {
    .ant-dropdown-menu {
      padding: 8px;
      border-radius: 8px;
      box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08);
    }

    .ant-dropdown-menu-item {
      padding: 8px 12px;
      border-radius: 6px;

      &:hover {
        background-color: #F9F5FF;
      }
    }
  }
}

.metricCard {
  height: 100%;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.descriptions {
  tr, td, th {
    border-color: transparent !important;
  }

  th {
    display: none;
  }

  .descriptionsLabel {
    font-size: 14px;
    font-weight: 600;
    line-height: 21px;
    color: $black;
  }

  .descriptionsValue {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    color: $black;
    white-space: pre-wrap;
  }
}

.metricType {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: $gray;
}

.dropdown {
  width: 224px;

  .dropdownTitle {
    color: $black;
  }

  .dropdownItem {
    padding: 8px !important;

    button {
      padding: 0;
    }
  }
}

.actionButton {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.metricCardContainer {
  &:hover {
    .actionButton {
      opacity: 1;
    }
  }
}

