import { useState, useCallback } from 'react';
import { useGeneralStore } from "@/providers/general-store-provider";
import { useAuthStore } from "@/stores/auth-store";
import { IMetric, EMetricType } from "@/types/metric";
import { IMetricDto } from "@/app/api/types/metricDto";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import { MetricState } from '../types';

const API_BASE_URL = '/api/agents';

export const useMetrics = () => {
  const { user } = useAuthStore();
  const { currentAgentId, metrics, setMetrics } = useGeneralStore((state) => ({
    currentAgentId: state.currentAgentId,
    metrics: state.metrics,
    setMetrics: state.setMetrics,
  }));

  const notify = useNotification();
  
  const [state, setState] = useState<Omit<MetricState, 'metrics'>>({
    loading: false,
    error: null,
    currentAgentId: null,
  });

  const formatMetric = (metric: IMetricDto): IMetric => ({
    id: String(metric.id),
    name: metric.name,
    metricPrompt: metric.prompt,
    metricType: metric.type as EMetricType,
  });

  const fetchMetrics = useCallback(async () => {
    if (!currentAgentId || !user?.token) {
      setState(prev => ({ ...prev, error: 'No agent selected or user not authenticated' }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const response = await fetch(`${API_BASE_URL}/${currentAgentId}/metrics`, {
        headers: { Authorization: `Bearer ${user.token}` },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: IMetricDto[] = await response.json();
      const formattedMetrics = data.map(formatMetric);

      setMetrics(formattedMetrics); // Only update the store
      setState(prev => ({ ...prev, loading: false }));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch metrics';
      setState(prev => ({ ...prev, loading: false, error: errorMessage }));
      notify.error({
        message: "Failed to fetch metrics",
        description: errorMessage,
      });
    }
  }, [currentAgentId, user?.token, setMetrics, notify]);

  const deleteMetric = useCallback(async (metricId: string) => {
    if (!currentAgentId || !user?.token) {
      setState(prev => ({ ...prev, error: 'No agent selected or user not authenticated' }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const response = await fetch(
        `${API_BASE_URL}/${currentAgentId}/metrics/${metricId}`,
        {
          method: "DELETE",
          headers: { Authorization: `Bearer ${user.token}` },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const updatedMetrics = metrics.filter(metric => metric.id !== metricId);
      setMetrics(updatedMetrics);
      setState(prev => ({ ...prev, loading: false }));
      notify.success({
        message: "Metric deleted successfully",
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete metric';
      setState(prev => ({ ...prev, loading: false, error: errorMessage }));
      notify.error({
        message: "Failed to delete metric",
        description: errorMessage,
      });
    }
  }, [currentAgentId, user?.token, metrics, setMetrics, notify]);

  return {
    metrics, // from the store
    loading: state.loading,
    error: state.error,
    fetchMetrics,
    deleteMetric,
  };
}; 