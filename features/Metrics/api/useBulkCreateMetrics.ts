import { IMetricDto } from "@/app/api/types/metricDto";
import { EMetricType } from "@/types/metric";
import { useQueryClient } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";
import { bulkCreateMetrics } from "../services/metricService";

/**
 * Bulk creates metrics from templates for an agent
 * @param agentId - The ID of the agent to bulk create metrics for
 * @param templates - An array of objects containing metric name, prompt, and type
 */
export function useBulkCreateMetricsMutation(agentId: string) {
    const queryClient = useQueryClient();
    return useMutation<IMetricDto[], Error, Array<{ name: string; prompt: string; type: EMetricType }>>({
        mutationFn: (templates) => bulkCreateMetrics(agentId, templates),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['metrics', agentId] });
        },
    });
}
