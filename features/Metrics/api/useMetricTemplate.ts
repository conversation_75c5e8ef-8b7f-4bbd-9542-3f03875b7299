import { IMetricDto } from "@/app/api/types/metricDto";
import {  fetchMetricTemplates } from "../services/metricService";
import { useQuery } from "@tanstack/react-query";

/**
 * Fetches all metrics for an agent
 * @param agentId - The ID of the agent to fetch metrics for
 * @returns A list of metrics for the agent
 */
export function useMetricTemplate(agentId?: string) {
    return useQuery<IMetricDto[], Error>({
        queryKey: ['metric-templates', agentId],
        queryFn: () => {
            if (!agentId) throw new Error('No agentId provided');
            return fetchMetricTemplates();
        },
        enabled: !!agentId,
    });
}