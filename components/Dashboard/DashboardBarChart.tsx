"use client";
import React from 'react';
import { Box, Typography } from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
} from 'recharts';

interface ChartDataPoint {
  month: string;
  year: number;
  runs?: number;
}


interface DashboardBarChartProps {
  data: ChartDataPoint[];
  title?: string;
  dataKey?: string;
  barColor?: string;
  xAxisTickFormatter?: (month: string) => string;
}

const CustomBarTooltip = ({ active, payload, label }: { active: boolean, payload: any[], label: string }) => {
  if (!active || !payload || !payload.length) return null;
  const item = payload[0];
  const { year } = item.payload;

  return (
    <div
      style={{
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        borderRadius: 12,
        border: '1px solid #F5F5F5',
        boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
        fontFamily: 'Inter',
        padding: '12px',
        width: 200,
      }}
    >
      <div
        style={{
          fontFamily: 'Inter',
          fontWeight: 500,
          fontSize: 12,
          color: '#414346',
          marginBottom: 12,
          textAlign: 'center',
        }}
      >
        {label} {year}
      </div>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: 8 }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <div
            style={{
              width: 4,
              height: 20,
              borderRadius: 20,
              background: '#9E77ED',
            }}
          />
          <div
            style={{
              fontFamily: 'Inter',
              fontWeight: 500,
              fontSize: 14,
              color: '#414346',
            }}
          >
            Number of Runs
          </div>
        </div>
        <div
          style={{
            fontFamily: 'Inter',
            fontWeight: 700,
            fontSize: 12,
            color: '#414346',
          }}
        >
          {item.value}
        </div>
      </div>
    </div>
  );
};

// Define the bar chart in a reusable component
const DashboardBarChart: React.FC<DashboardBarChartProps> = ({
  data,
  title = 'Latency',
  dataKey = 'total_latency_ms',
  xAxisTickFormatter,
}) => {
  // Generate empty data structure when no data is provided
  const currentYear = new Date().getFullYear();
  const emptyData = [
    { month: 'Jan', year: currentYear, [dataKey]: 0 },
    { month: 'Feb', year: currentYear, [dataKey]: 0 },
    { month: 'Mar', year: currentYear, [dataKey]: 0 },
    { month: 'Apr', year: currentYear, [dataKey]: 0 },
    { month: 'May', year: currentYear, [dataKey]: 0 },
    { month: 'Jun', year: currentYear, [dataKey]: 0 },
    { month: 'Jul', year: currentYear, [dataKey]: 0 },
    { month: 'Aug', year: currentYear, [dataKey]: 0 },
    { month: 'Sep', year: currentYear, [dataKey]: 0 },
    { month: 'Oct', year: currentYear, [dataKey]: 0 },
    { month: 'Nov', year: currentYear, [dataKey]: 0 },
    { month: 'Dec', year: currentYear, [dataKey]: 0 },
  ];

  // Always use full 12-month structure, merge with provided data
  const chartData = emptyData.map(emptyMonth => {
    const existingData = data?.find(item => item.month === emptyMonth.month);
    return existingData || emptyMonth;
  });
  const isEmpty = !data || data.length === 0;

  return (
    <Box
      sx={{
        p: '28px',
        borderRadius: '32px',
        backgroundColor: '#FFFFFF',
        border: '1px solid #E5E6E6',
        overflow: 'hidden',
      }}
    >
      {/* Title / Section Heading */}
      <Typography
        sx={{
          mb: 3,
          fontSize: '16px',
          fontWeight: 700,
          fontFamily: 'Plus Jakarta Sans',
          color: '#242E2C',
        }}
      >
        {title}
      </Typography>

      <Box sx={{ width: '100%', height: 360 }}>
        <ResponsiveContainer>
          <BarChart
            data={chartData}
            margin={{ top: 0, right: 0, bottom: 0, left: 0 }}
            barGap={0}
            barCategoryGap="5%"
          >
            {/* Light grid lines */}
            <CartesianGrid
              vertical={false}
              stroke="#E6E6E6"
              strokeOpacity={1}
              strokeWidth={1}
            />

            {/* X-axis for months */}
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              stroke="#414346"
              tick={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 600,
                fontSize: 14,
                letterSpacing: 0,
              }}
              tickFormatter={xAxisTickFormatter}
            />

            {/* Y-axis for runs (auto domain based on data) */}
            <YAxis
              tickLine={false}
              axisLine={false}
              stroke="#414346"
              tick={{
                fontFamily: 'Plus Jakarta Sans',
                fontSize: 14,
                fontWeight: 600,
              }}
              domain={isEmpty ? [0, 100] : undefined}
              ticks={isEmpty ? [0, 20, 40, 60, 80, 100] : undefined}
            />

            {/* Tooltip for month, year, runs */}
            <Tooltip
              cursor={{ fill: 'transparent' }}
              content={<CustomBarTooltip active={true} payload={[]} label={''} />}
            />

            {/* Bar Group - always render to show months on X-axis */}
            <Bar
              dataKey={dataKey}
              name={title}
              radius={[6, 6, 0, 0]}
              fill={'#9E77ED'}
              barSize={48}
            />
          </BarChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  );
};

export default DashboardBarChart;
